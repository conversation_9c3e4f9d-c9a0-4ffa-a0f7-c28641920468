<!-- Search Bar -->
<div *ngIf="searchable" class="mb-4">
  <div class="relative">
    <input
      type="text"
      [(ngModel)]="searchTerm"
      (input)="onSearch()"
      placeholder="Search..."
      class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
    >
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
    </div>
  </div>
</div>

<!-- Table Container -->
<div class="bg-white shadow-sm rounded-lg overflow-hidden">
  <!-- Loading State -->
  <div *ngIf="loading" class="p-8 text-center">
    <div class="inline-flex items-center">
      <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      {{ loadingMessage }}
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && data.length === 0" class="p-8 text-center text-gray-500">
    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1"></path>
    </svg>
    <p>{{ emptyMessage }}</p>
  </div>

  <!-- Table -->
  <div *ngIf="!loading && data.length > 0" class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <!-- Table Header -->
      <thead class="bg-gray-50">
        <tr>
          <!-- Selection Column -->
          <th *ngIf="selectable" class="px-6 py-3 text-left">
            <input
              type="checkbox"
              [checked]="allSelected"
              (change)="onSelectAll()"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
          </th>
          
          <!-- Data Columns -->
          <th
            *ngFor="let column of columns"
            [style.width]="column.width"
            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
            [ngClass]="getColumnAlignment(column)"
          >
            <div
              class="flex items-center space-x-1"
              [class.cursor-pointer]="column.sortable"
              (click)="onSort(column)"
            >
              <span>{{ column.label }}</span>
              <svg
                *ngIf="column.sortable"
                class="w-4 h-4"
                [class.text-blue-500]="sortColumn === column.key"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path [attr.d]="getSortIcon(column)"></path>
              </svg>
            </div>
          </th>
          
          <!-- Actions Column -->
          <th *ngIf="actions.length > 0" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      
      <!-- Table Body -->
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let item of data; trackBy: trackByFn" class="hover:bg-gray-50">
          <!-- Selection Column -->
          <td *ngIf="selectable" class="px-6 py-4">
            <input
              type="checkbox"
              [checked]="isSelected(item)"
              (change)="onSelectItem(item)"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
          </td>
          
          <!-- Data Columns -->
          <td
            *ngFor="let column of columns"
            class="px-6 py-4 whitespace-nowrap"
            [ngClass]="getColumnAlignment(column)"
          >
            <!-- Text/Number/Currency/Date -->
            <span *ngIf="column.type === 'text' || column.type === 'number' || column.type === 'currency' || column.type === 'date' || !column.type"
                  class="text-sm text-gray-900">
              {{ getCellValue(item, column) }}
            </span>
            
            <!-- Boolean -->
            <span *ngIf="column.type === 'boolean'"
                  [class]="getCellValue(item, column) === 'Yes' ? 'text-green-600' : 'text-red-600'"
                  class="text-sm font-medium">
              {{ getCellValue(item, column) }}
            </span>
            
            <!-- Badge -->
            <span *ngIf="column.type === 'badge'"
                  [class]="getBadgeClass(getNestedValue(item, column.key), column)"
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ getCellValue(item, column) }}
            </span>
            
            <!-- Image -->
            <img *ngIf="column.type === 'image'"
                 [src]="getNestedValue(item, column.key)"
                 [alt]="item.name || 'Image'"
                 class="h-10 w-10 rounded-lg object-cover"
                 (error)="$event.target.src='assets/images/placeholder.png'">
          </td>
          
          <!-- Actions Column -->
          <td *ngIf="actions.length > 0" class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <div class="flex justify-end space-x-2">
              <button
                *ngFor="let action of actions"
                *ngIf="isActionVisible(action, item)"
                (click)="executeAction(action, item)"
                [class]="getActionClass(action)"
                [title]="action.label"
              >
                <svg *ngIf="action.icon" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="action.icon"></path>
                </svg>
                <span *ngIf="!action.icon">{{ action.label }}</span>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
